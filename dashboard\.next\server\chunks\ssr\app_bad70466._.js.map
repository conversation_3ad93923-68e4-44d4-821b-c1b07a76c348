{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport {\r\n  ClockIcon,\r\n  UserIcon,\r\n  ScaleIcon,\r\n  ChatBubbleLeftRightIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Case } from \"../../_lib/types\";\r\n\r\ninterface CaseCardProps {\r\n  case: Case;\r\n}\r\n\r\nconst priorityColors = {\r\n  low: \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\",\r\n  medium:\r\n    \"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\",\r\n  high: \"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\",\r\n};\r\n\r\nconst typeColors = {\r\n  Laboral: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\",\r\n  Civil:\r\n    \"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200\",\r\n  Familia: \"bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200\",\r\n  Penal: \"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\",\r\n  Comercial:\r\n    \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\",\r\n};\r\n\r\nexport function CaseCard({\r\n  case: caseData,\r\n}: CaseCardProps) {\r\n  const formattedDate = format(\r\n    new Date(caseData.createdAt),\r\n    \"dd MMM yyyy - HH:mm\",\r\n    {\r\n      locale: es,\r\n    }\r\n  );\r\n\r\n  // Verificar si es un caso creado por el usuario\r\n  const isUserCreatedCase = () => {\r\n    // Los casos creados por usuario tienen IDs con formato: c-xxxxxxxx (8 caracteres aleatorios)\r\n    // Los casos del JSON tienen IDs como: c-001, c-002, etc.\r\n    const idPattern = /^c-[a-f0-9]{8}$/i;\r\n    return idPattern.test(caseData.id);\r\n  };\r\n\r\n  return (\r\n    <div className=\"group relative bg-white dark:bg-gray-800 border-0 rounded-xl p-5 shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden\">\r\n      {/* Gradient border effect */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n\r\n      {/* Left border accent */}\r\n      <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-xl ${\r\n        caseData.priority === \"high\"\r\n          ? \"bg-gradient-to-b from-red-500 to-red-600\"\r\n          : caseData.priority === \"medium\"\r\n          ? \"bg-gradient-to-b from-yellow-500 to-orange-500\"\r\n          : \"bg-gradient-to-b from-green-500 to-emerald-500\"\r\n      }`} />\r\n\r\n      <Link href={`/dashboard/${caseData.id}`} className=\"block relative z-10\">\r\n        <div className=\"space-y-4\">\r\n          {/* Header with priority and notifications */}\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1 pr-3\">\r\n              <h4 className=\"font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 mb-2\">\r\n                {caseData.title}\r\n              </h4>\r\n\r\n              {/* Type badge */}\r\n              <span\r\n                className={`inline-flex items-center text-xs font-medium px-2.5 py-1 rounded-lg ${\r\n                  typeColors[caseData.type as keyof typeof typeColors] ||\r\n                  \"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\r\n                }`}\r\n              >\r\n                {caseData.type}\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col items-end space-y-2\">\r\n              {/* Priority badge */}\r\n              <span\r\n                className={`text-xs font-semibold px-2.5 py-1 rounded-lg shadow-sm ${\r\n                  priorityColors[caseData.priority]\r\n                }`}\r\n              >\r\n                {caseData.priority === \"high\"\r\n                  ? \"Alta\"\r\n                  : caseData.priority === \"medium\"\r\n                  ? \"Media\"\r\n                  : \"Baja\"}\r\n              </span>\r\n\r\n              {/* Unread Messages Badge - oculto para casos creados por usuario */}\r\n              {!isUserCreatedCase() && caseData.unreadMessagesCount && caseData.unreadMessagesCount > 0 && (\r\n                <div className=\"relative\">\r\n                  <div className=\"bg-red-500 dark:bg-red-600 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center shadow-lg\">\r\n                    {caseData.unreadMessagesCount > 9 ? \"9+\" : caseData.unreadMessagesCount}\r\n                  </div>\r\n                  <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-400 rounded-full animate-ping\" />\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Client info */}\r\n          <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-2\">\r\n            <UserIcon className=\"h-4 w-4 mr-2 text-gray-400 dark:text-gray-500\" />\r\n            <span className=\"font-medium\">{caseData.client}</span>\r\n          </div>\r\n\r\n\r\n\r\n          {/* Footer with metadata */}\r\n          <div className=\"flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700\">\r\n            <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400\">\r\n              <ClockIcon className=\"h-3 w-3 mr-1\" />\r\n              {formattedDate}\r\n            </div>\r\n\r\n            {caseData.similarCount > 0 && (\r\n              <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md\">\r\n                <ScaleIcon className=\"h-3 w-3 mr-1\" />\r\n                {caseData.similarCount} similares\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Messages indicator - oculto para casos creados por usuario */}\r\n          {!isUserCreatedCase() && (() => {\r\n            const messageCount = caseData.unreadMessagesCount || 0;\r\n            const hasUnread = messageCount > 0;\r\n\r\n            return (\r\n              <div className={`flex items-center justify-center text-xs rounded-lg py-2 border ${\r\n                hasUnread\r\n                  ? \"text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800\"\r\n                  : \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800\"\r\n              }`}>\r\n                <ChatBubbleLeftRightIcon className=\"h-3 w-3 mr-1\" />\r\n                <span className=\"font-medium\">\r\n                  {hasUnread\r\n                    ? `${messageCount} mensaje${messageCount !== 1 ? \"s\" : \"\"} sin leer`\r\n                    : \"Sin mensajes pendientes\"\r\n                  }\r\n                </span>\r\n              </div>\r\n            );\r\n          })()}\r\n        </div>\r\n      </Link>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,iBAAiB;IACrB,KAAK;IACL,QACE;IACF,MAAM;AACR;AAEA,MAAM,aAAa;IACjB,SAAS;IACT,OACE;IACF,SAAS;IACT,OAAO;IACP,WACE;AACJ;AAEO,SAAS,SAAS,EACvB,MAAM,QAAQ,EACA;IACd,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EACzB,IAAI,KAAK,SAAS,SAAS,GAC3B,uBACA;QACE,QAAQ,2IAAA,CAAA,KAAE;IACZ;IAGF,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,6FAA6F;QAC7F,yDAAyD;QACzD,MAAM,YAAY;QAClB,OAAO,UAAU,IAAI,CAAC,SAAS,EAAE;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAW,CAAC,gDAAgD,EAC/D,SAAS,QAAQ,KAAK,SAClB,6CACA,SAAS,QAAQ,KAAK,WACtB,mDACA,kDACJ;;;;;;0BAEF,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;gBAAE,WAAU;0BACjD,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAIjB,8OAAC;4CACC,WAAW,CAAC,oEAAoE,EAC9E,UAAU,CAAC,SAAS,IAAI,CAA4B,IACpD,iEACA;sDAED,SAAS,IAAI;;;;;;;;;;;;8CAIlB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAW,CAAC,uDAAuD,EACjE,cAAc,CAAC,SAAS,QAAQ,CAAC,EACjC;sDAED,SAAS,QAAQ,KAAK,SACnB,SACA,SAAS,QAAQ,KAAK,WACtB,UACA;;;;;;wCAIL,CAAC,uBAAuB,SAAS,mBAAmB,IAAI,SAAS,mBAAmB,GAAG,mBACtF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,SAAS,mBAAmB,GAAG,IAAI,OAAO,SAAS,mBAAmB;;;;;;8DAEzE,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,+MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAe,SAAS,MAAM;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACpB;;;;;;;gCAGF,SAAS,YAAY,GAAG,mBACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACpB,SAAS,YAAY;wCAAC;;;;;;;;;;;;;wBAM5B,CAAC,uBAAuB,CAAC;4BACxB,MAAM,eAAe,SAAS,mBAAmB,IAAI;4BACrD,MAAM,YAAY,eAAe;4BAEjC,qBACE,8OAAC;gCAAI,WAAW,CAAC,gEAAgE,EAC/E,YACI,mGACA,8GACJ;;kDACA,8OAAC,6OAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDACb,YACG,GAAG,aAAa,QAAQ,EAAE,iBAAiB,IAAI,MAAM,GAAG,SAAS,CAAC,GAClE;;;;;;;;;;;;wBAKZ,CAAC;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/AddCaseModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  XMarkIcon,\n  CheckCircleIcon,\n  PlusIcon,\n  CalendarIcon,\n  UserIcon,\n  ScaleIcon,\n} from \"@heroicons/react/24/outline\";\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport { Case } from \"../../_lib/types\";\n\ninterface AddCaseModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onCaseAdded: (newCase: Case) => void;\n}\n\ninterface FormData {\n  title: string;\n  type: string;\n  client: string;\n  description: string;\n  priority: \"low\" | \"medium\" | \"high\";\n  estimatedCost: string;\n  milestones: {\n    title: string;\n    dueDate: string;\n  }[];\n}\n\nconst CASE_TYPES = [\n  \"Laboral\",\n  \"Civil\",\n  \"Familia\",\n  \"Penal\",\n  \"Comercial\",\n  \"Administrativo\",\n  \"Constitucional\",\n  \"Tributario\",\n];\n\nconst PRIORITIES = [\n  { value: \"low\", label: \"Baja\", color: \"text-green-600 dark:text-green-400\" },\n  { value: \"medium\", label: \"Media\", color: \"text-yellow-600 dark:text-yellow-400\" },\n  { value: \"high\", label: \"Alta\", color: \"text-red-600 dark:text-red-400\" },\n];\n\nexport function AddCaseModal({ isOpen, onClose, onCaseAdded }: AddCaseModalProps) {\n  const [step, setStep] = useState<\"basic\" | \"details\" | \"milestones\">(\"basic\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [formData, setFormData] = useState<FormData>({\n    title: \"\",\n    type: \"\",\n    client: \"\",\n    description: \"\",\n    priority: \"medium\",\n    estimatedCost: \"\",\n    milestones: [\n      { title: \"\", dueDate: \"\" },\n    ],\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const validateStep = (currentStep: string): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (currentStep === \"basic\") {\n      if (!formData.title.trim()) newErrors.title = \"El título es obligatorio\";\n      if (!formData.type) newErrors.type = \"El tipo de caso es obligatorio\";\n      if (!formData.client.trim()) newErrors.client = \"El nombre del cliente es obligatorio\";\n    }\n\n    if (currentStep === \"details\") {\n      if (!formData.description.trim()) newErrors.description = \"La descripción es obligatoria\";\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNext = () => {\n    if (!validateStep(step)) return;\n\n    if (step === \"basic\") setStep(\"details\");\n    else if (step === \"details\") setStep(\"milestones\");\n    else if (step === \"milestones\") handleSubmit();\n  };\n\n  const handleBack = () => {\n    if (step === \"details\") setStep(\"basic\");\n    else if (step === \"milestones\") setStep(\"details\");\n  };\n\n  const addMilestone = () => {\n    setFormData(prev => ({\n      ...prev,\n      milestones: [...prev.milestones, { title: \"\", dueDate: \"\" }],\n    }));\n  };\n\n  const removeMilestone = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      milestones: prev.milestones.filter((_, i) => i !== index),\n    }));\n  };\n\n  const updateMilestone = (index: number, field: \"title\" | \"dueDate\", value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      milestones: prev.milestones.map((milestone, i) =>\n        i === index ? { ...milestone, [field]: value } : milestone\n      ),\n    }));\n  };\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true);\n\n    try {\n      // Crear el nuevo caso\n      const newCase: Case = {\n        id: `c-${crypto.randomUUID().slice(0, 8)}`,\n        title: formData.title,\n        type: formData.type,\n        client: formData.client,\n        status: \"new\",\n        progress: 0,\n        createdAt: new Date().toISOString(),\n        similarCount: 0,\n        description: formData.description,\n        priority: formData.priority,\n        estimatedCost: formData.estimatedCost || undefined,\n        complexityScore: Math.floor(Math.random() * 10) + 1, // Simulado\n        riskAssessment: formData.priority === \"high\" ? \"high\" : formData.priority === \"low\" ? \"low\" : \"medium\",\n        successProbability: Math.floor(Math.random() * 30) + 70, // 70-100%\n        aiSummary: `Caso de ${formData.type.toLowerCase()} con prioridad ${formData.priority === \"high\" ? \"alta\" : formData.priority === \"medium\" ? \"media\" : \"baja\"}. ${formData.description.slice(0, 100)}${formData.description.length > 100 ? \"...\" : \"\"}`,\n        keyFacts: [\n          `Tipo de caso: ${formData.type}`,\n          `Cliente: ${formData.client}`,\n          `Prioridad: ${formData.priority === \"high\" ? \"Alta\" : formData.priority === \"medium\" ? \"Media\" : \"Baja\"}`,\n          formData.estimatedCost ? `Costo estimado: ${formData.estimatedCost}` : \"Costo por definir\",\n        ],\n        nextActions: [\n          \"Revisar documentación inicial\",\n          \"Programar reunión con el cliente\",\n          \"Definir estrategia legal\",\n          \"Establecer cronograma de trabajo\",\n        ],\n        milestones: formData.milestones\n          .filter(m => m.title.trim())\n          .map((milestone, index) => ({\n            id: `m-${crypto.randomUUID().slice(0, 8)}-${index + 1}`,\n            title: milestone.title,\n            completed: false,\n            dueDate: milestone.dueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 días por defecto\n            dependencies: index > 0 ? [`m-${crypto.randomUUID().slice(0, 8)}-${index}`] : [],\n          })),\n        messages: [\n          {\n            id: `msg-${crypto.randomUUID()}`,\n            sender: \"lawyer\",\n            content: `Hola ${formData.client}, he creado tu caso \"${formData.title}\". Estaré en contacto contigo para coordinar los próximos pasos.`,\n            timestamp: new Date().toISOString(),\n            status: \"sent\",\n          },\n        ],\n        documents: [],\n        activities: [\n          {\n            id: `act-${crypto.randomUUID()}`,\n            type: \"status_change\",\n            description: \"Caso creado\",\n            timestamp: new Date().toISOString(),\n            user: \"lawyer\",\n          },\n        ],\n        unreadMessagesCount: 0,\n      };\n\n      // Simular delay de creación\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // Guardar en sessionStorage (persistencia durante la sesión)\n      const existingCases = JSON.parse(sessionStorage.getItem(\"cases\") || \"[]\");\n      const updatedCases = [newCase, ...existingCases];\n      sessionStorage.setItem(\"cases\", JSON.stringify(updatedCases));\n\n      // Notificar al componente padre\n      onCaseAdded(newCase);\n\n      // Cerrar modal directamente después de crear el caso\n      handleClose();\n    } catch (error) {\n      console.error(\"Error al crear el caso:\", error);\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    // Reset form\n    setFormData({\n      title: \"\",\n      type: \"\",\n      client: \"\",\n      description: \"\",\n      priority: \"medium\",\n      estimatedCost: \"\",\n      milestones: [{ title: \"\", dueDate: \"\" }],\n    });\n    setStep(\"basic\");\n    setErrors({});\n    setIsSubmitting(false);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  const getStepTitle = () => {\n    switch (step) {\n      case \"basic\": return \"Información Básica\";\n      case \"details\": return \"Detalles del Caso\";\n      case \"milestones\": return \"Tareas y Plazos\";\n      default: return \"\";\n    }\n  };\n\n  const renderStepIndicator = () => (\n    <div className=\"flex items-center justify-center mb-6\">\n      {[\"basic\", \"details\", \"milestones\"].map((stepName, index) => (\n        <div key={stepName} className=\"flex items-center\">\n          <div\n            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n              step === stepName\n                ? \"bg-blue-600 text-white\"\n                : index < [\"basic\", \"details\", \"milestones\"].indexOf(step)\n                ? \"bg-green-600 text-white\"\n                : \"bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400\"\n            }`}\n          >\n            {index < [\"basic\", \"details\", \"milestones\"].indexOf(step) ? (\n              <CheckCircleIcon className=\"h-5 w-5\" />\n            ) : (\n              index + 1\n            )}\n          </div>\n          {index < 2 && (\n            <div\n              className={`w-12 h-0.5 mx-2 ${\n                index < [\"basic\", \"details\", \"milestones\"].indexOf(step)\n                  ? \"bg-green-600\"\n                  : \"bg-gray-200 dark:bg-gray-700\"\n              }`}\n            />\n          )}\n        </div>\n      ))}\n    </div>\n  );\n\n  return (\n    <Dialog.Root open={isOpen} onOpenChange={handleClose}>\n      <Dialog.Portal>\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 dark:bg-black/70 z-50\" />\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <Dialog.Title className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n              {getStepTitle()}\n            </Dialog.Title>\n            <Dialog.Close asChild>\n              <button className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\">\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </Dialog.Close>\n          </div>\n\n          {renderStepIndicator()}\n\n          {/* Step Content */}\n          <div className=\"space-y-6\">\n            {step === \"basic\" && (\n              <motion.div\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"space-y-4\"\n              >\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    <UserIcon className=\"h-4 w-4 inline mr-1\" />\n                    Título del Caso *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.title}\n                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.title ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Ej: Despido sin causa - Empresa XYZ\"\n                  />\n                  {errors.title && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.title}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    <ScaleIcon className=\"h-4 w-4 inline mr-1\" />\n                    Tipo de Caso *\n                  </label>\n                  <select\n                    value={formData.type}\n                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.type ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                  >\n                    <option value=\"\">Seleccionar tipo de caso</option>\n                    {CASE_TYPES.map(type => (\n                      <option key={type} value={type}>{type}</option>\n                    ))}\n                  </select>\n                  {errors.type && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.type}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    <UserIcon className=\"h-4 w-4 inline mr-1\" />\n                    Cliente *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.client}\n                    onChange={(e) => setFormData(prev => ({ ...prev, client: e.target.value }))}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.client ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Nombre completo del cliente\"\n                  />\n                  {errors.client && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.client}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Prioridad\n                  </label>\n                  <select\n                    value={formData.priority}\n                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as \"low\" | \"medium\" | \"high\" }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {PRIORITIES.map(priority => (\n                      <option key={priority.value} value={priority.value}>\n                        {priority.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </motion.div>\n            )}\n\n            {step === \"details\" && (\n              <motion.div\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"space-y-4\"\n              >\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Descripción del Caso *\n                  </label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    rows={4}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${\n                      errors.description ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Describe los detalles del caso, antecedentes, situación actual...\"\n                  />\n                  {errors.description && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.description}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Costo Estimado (Opcional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.estimatedCost}\n                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedCost: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Ej: ARS 150,000 - 200,000\"\n                  />\n                </div>\n              </motion.div>\n            )}\n\n            {step === \"milestones\" && (\n              <motion.div\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"space-y-4\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Tareas y Plazos (Opcional)\n                  </h4>\n                  <button\n                    type=\"button\"\n                    onClick={addMilestone}\n                    className=\"flex items-center px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors cursor-pointer\"\n                  >\n                    <PlusIcon className=\"h-4 w-4 mr-1\" />\n                    Agregar Tarea\n                  </button>\n                </div>\n\n                <div className=\"space-y-3 max-h-60 overflow-y-auto\">\n                  {formData.milestones.map((milestone, index) => (\n                    <div key={index} className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\">\n                      <div className=\"flex-1\">\n                        <input\n                          type=\"text\"\n                          value={milestone.title}\n                          onChange={(e) => updateMilestone(index, \"title\", e.target.value)}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                          placeholder=\"Nombre de la tarea\"\n                        />\n                      </div>\n                      <div className=\"w-32\">\n                        <input\n                          type=\"date\"\n                          value={milestone.dueDate}\n                          onChange={(e) => updateMilestone(index, \"dueDate\", e.target.value)}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                        />\n                      </div>\n                      {formData.milestones.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeMilestone(index)}\n                          className=\"text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 p-1 rounded cursor-pointer\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {formData.milestones.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                    <CalendarIcon className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\n                    <p className=\"text-sm\">No hay tareas agregadas</p>\n                    <button\n                      type=\"button\"\n                      onClick={addMilestone}\n                      className=\"mt-2 text-blue-600 dark:text-blue-400 hover:underline cursor-pointer\"\n                    >\n                      Agregar primera tarea\n                    </button>\n                  </div>\n                )}\n              </motion.div>\n            )}\n\n\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700\">\n            {step !== \"basic\" && (\n              <button\n                type=\"button\"\n                onClick={handleBack}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\n              >\n                Anterior\n              </button>\n            )}\n\n            <div className=\"flex-1\" />\n\n            <button\n              type=\"button\"\n              onClick={handleNext}\n              disabled={isSubmitting}\n              className=\"px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer flex items-center\"\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Creando...\n                </>\n              ) : step === \"milestones\" ? (\n                \"Crear Caso\"\n              ) : (\n                \"Siguiente\"\n              )}\n            </button>\n          </div>\n        </Dialog.Content>\n      </Dialog.Portal>\n    </Dialog.Root>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAZA;;;;;;AAkCA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;QAAQ,OAAO;IAAqC;IAC3E;QAAE,OAAO;QAAU,OAAO;QAAS,OAAO;IAAuC;IACjF;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAiC;CACzE;AAEM,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAqB;IAC9E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,MAAM;QACN,QAAQ;QACR,aAAa;QACb,UAAU;QACV,eAAe;QACf,YAAY;YACV;gBAAE,OAAO;gBAAI,SAAS;YAAG;SAC1B;IACH;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe,CAAC;QACpB,MAAM,YAAoC,CAAC;QAE3C,IAAI,gBAAgB,SAAS;YAC3B,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;YAC9C,IAAI,CAAC,SAAS,IAAI,EAAE,UAAU,IAAI,GAAG;YACrC,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,UAAU,MAAM,GAAG;QAClD;QAEA,IAAI,gBAAgB,WAAW;YAC7B,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC5D;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,aAAa,OAAO;QAEzB,IAAI,SAAS,SAAS,QAAQ;aACzB,IAAI,SAAS,WAAW,QAAQ;aAChC,IAAI,SAAS,cAAc;IAClC;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS,WAAW,QAAQ;aAC3B,IAAI,SAAS,cAAc,QAAQ;IAC1C;IAEA,MAAM,eAAe;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;wBAAE,OAAO;wBAAI,SAAS;oBAAG;iBAAE;YAC9D,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACrD,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,OAAe,OAA4B;QAClE,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,IAC1C,MAAM,QAAQ;wBAAE,GAAG,SAAS;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAErD,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAEhB,IAAI;YACF,sBAAsB;YACtB,MAAM,UAAgB;gBACpB,IAAI,CAAC,EAAE,EAAE,OAAO,UAAU,GAAG,KAAK,CAAC,GAAG,IAAI;gBAC1C,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,QAAQ;gBACR,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,cAAc;gBACd,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,aAAa,IAAI;gBACzC,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAClD,gBAAgB,SAAS,QAAQ,KAAK,SAAS,SAAS,SAAS,QAAQ,KAAK,QAAQ,QAAQ;gBAC9F,oBAAoB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBACrD,WAAW,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG,eAAe,EAAE,SAAS,QAAQ,KAAK,SAAS,SAAS,SAAS,QAAQ,KAAK,WAAW,UAAU,OAAO,EAAE,EAAE,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,OAAO,SAAS,WAAW,CAAC,MAAM,GAAG,MAAM,QAAQ,IAAI;gBACtP,UAAU;oBACR,CAAC,cAAc,EAAE,SAAS,IAAI,EAAE;oBAChC,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;oBAC7B,CAAC,WAAW,EAAE,SAAS,QAAQ,KAAK,SAAS,SAAS,SAAS,QAAQ,KAAK,WAAW,UAAU,QAAQ;oBACzG,SAAS,aAAa,GAAG,CAAC,gBAAgB,EAAE,SAAS,aAAa,EAAE,GAAG;iBACxE;gBACD,aAAa;oBACX;oBACA;oBACA;oBACA;iBACD;gBACD,YAAY,SAAS,UAAU,CAC5B,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,IAAI,IACxB,GAAG,CAAC,CAAC,WAAW,QAAU,CAAC;wBAC1B,IAAI,CAAC,EAAE,EAAE,OAAO,UAAU,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,GAAG;wBACvD,OAAO,UAAU,KAAK;wBACtB,WAAW;wBACX,SAAS,UAAU,OAAO,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;wBACxF,cAAc,QAAQ,IAAI;4BAAC,CAAC,EAAE,EAAE,OAAO,UAAU,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO;yBAAC,GAAG,EAAE;oBAClF,CAAC;gBACH,UAAU;oBACR;wBACE,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;wBAChC,QAAQ;wBACR,SAAS,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,qBAAqB,EAAE,SAAS,KAAK,CAAC,gEAAgE,CAAC;wBACxI,WAAW,IAAI,OAAO,WAAW;wBACjC,QAAQ;oBACV;iBACD;gBACD,WAAW,EAAE;gBACb,YAAY;oBACV;wBACE,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;wBAChC,MAAM;wBACN,aAAa;wBACb,WAAW,IAAI,OAAO,WAAW;wBACjC,MAAM;oBACR;iBACD;gBACD,qBAAqB;YACvB;YAEA,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,6DAA6D;YAC7D,MAAM,gBAAgB,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,YAAY;YACpE,MAAM,eAAe;gBAAC;mBAAY;aAAc;YAChD,eAAe,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;YAE/C,gCAAgC;YAChC,YAAY;YAEZ,qDAAqD;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,YAAY;YACV,OAAO;YACP,MAAM;YACN,QAAQ;YACR,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY;gBAAC;oBAAE,OAAO;oBAAI,SAAS;gBAAG;aAAE;QAC1C;QACA,QAAQ;QACR,UAAU,CAAC;QACX,gBAAgB;QAChB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAS;gBAAW;aAAa,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjD,8OAAC;oBAAmB,WAAU;;sCAC5B,8OAAC;4BACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,WACL,2BACA,QAAQ;gCAAC;gCAAS;gCAAW;6BAAa,CAAC,OAAO,CAAC,QACnD,4BACA,iEACJ;sCAED,QAAQ;gCAAC;gCAAS;gCAAW;6BAAa,CAAC,OAAO,CAAC,sBAClD,8OAAC,6NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;uCAE3B,QAAQ;;;;;;wBAGX,QAAQ,mBACP,8OAAC;4BACC,WAAW,CAAC,gBAAgB,EAC1B,QAAQ;gCAAC;gCAAS;gCAAW;6BAAa,CAAC,OAAO,CAAC,QAC/C,iBACA,gCACJ;;;;;;;mBAtBE;;;;;;;;;;IA8BhB,qBACE,8OAAC,kKAAA,CAAA,OAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,8OAAC,kKAAA,CAAA,SAAa;;8BACZ,8OAAC,kKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,8OAAC,kKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kKAAA,CAAA,QAAY;oCAAC,WAAU;8CACrB;;;;;;8CAEH,8OAAC,kKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wBAK1B;sCAGD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,yBACR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;;sDAEV,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;;sEACf,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;8DAG9C,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACxE,WAAW,CAAC,kOAAkO,EAC5O,OAAO,KAAK,GAAG,mBAAmB,wCAClC;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,KAAK;;;;;;;;;;;;sDAI5E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;;sEACf,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;8DAG/C,8OAAC;oDACC,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACvE,WAAW,CAAC,mLAAmL,EAC7L,OAAO,IAAI,GAAG,mBAAmB,wCACjC;;sEAEF,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;gEAAkB,OAAO;0EAAO;+DAApB;;;;;;;;;;;gDAGhB,OAAO,IAAI,kBACV,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,IAAI;;;;;;;;;;;;sDAI3E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;;sEACf,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;8DAG9C,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,MAAM;oDACtB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACzE,WAAW,CAAC,kOAAkO,EAC5O,OAAO,MAAM,GAAG,mBAAmB,wCACnC;oDACF,aAAY;;;;;;gDAEb,OAAO,MAAM,kBACZ,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,MAAM;;;;;;;;;;;;sDAI7E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAA8B,CAAC;oDACxG,WAAU;8DAET,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;4DAA4B,OAAO,SAAS,KAAK;sEAC/C,SAAS,KAAK;2DADJ,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;gCASpC,SAAS,2BACR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;;sDAEV,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC9E,MAAM;oDACN,WAAW,CAAC,8OAA8O,EACxP,OAAO,WAAW,GAAG,mBAAmB,wCACxC;oDACF,aAAY;;;;;;gDAEb,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,WAAW;;;;;;;;;;;;sDAIlF,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAChF,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;gCAMnB,SAAS,8BACR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuD;;;;;;8DAGrE,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;sDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,OAAO,UAAU,KAAK;gEACtB,UAAU,CAAC,IAAM,gBAAgB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC/D,WAAU;gEACV,aAAY;;;;;;;;;;;sEAGhB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,OAAO,UAAU,OAAO;gEACxB,UAAU,CAAC,IAAM,gBAAgB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;gEACjE,WAAU;;;;;;;;;;;wDAGb,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;sEAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;mDAxBjB;;;;;;;;;;wCA+Bb,SAAS,UAAU,CAAC,MAAM,KAAK,mBAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;sCAYX,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,yBACR,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAKH,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAuE;;uDAGtF,SAAS,eACX,eAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { CaseCard } from \"./CaseCard\";\r\nimport { Case } from \"../../_lib/types\";\r\nimport { ChevronLeftIcon, ChevronRightIcon, PlusIcon } from \"@heroicons/react/24/outline\";\r\nimport { AddCaseModal } from \"../modals/AddCaseModal\";\r\n\r\nconst columns = [\r\n  { id: \"new\", title: \"Nuevos\", status: \"new\" as const },\r\n  { id: \"in_progress\", title: \"En Curso\", status: \"in_progress\" as const },\r\n  { id: \"closed\", title: \"Cerrados\", status: \"closed\" as const },\r\n];\r\n\r\n\r\n\r\nexport function CaseList() {\r\n  const [cases, setCases] = useState<Case[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [activeMobileTab, setActiveMobileTab] = useState(0); // Para navegación móvil\r\n  const [showAddCaseModal, setShowAddCaseModal] = useState(false);\r\n\r\n  // Load cases from JSON file and localStorage\r\n  useEffect(() => {\r\n    const loadCases = async () => {\r\n      try {\r\n        // Cargar casos desde JSON\r\n        const response = await fetch(\"/data/cases.json\");\r\n        const casesData = await response.json();\r\n\r\n        // Cargar casos adicionales desde sessionStorage\r\n        const localCases = JSON.parse(sessionStorage.getItem(\"cases\") || \"[]\");\r\n\r\n        // Combinar casos (localStorage primero para que aparezcan arriba)\r\n        const allCases = [...localCases, ...casesData];\r\n        setCases(allCases);\r\n      } catch (error) {\r\n        console.error(\"Error loading cases:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadCases();\r\n  }, []);\r\n\r\n  const handleCaseAdded = (newCase: Case) => {\r\n    // Agregar el nuevo caso al principio de la lista\r\n    setCases(prev => [newCase, ...prev]);\r\n    setShowAddCaseModal(false);\r\n  };\r\n\r\n  const getCasesByStatus = (status: string) => {\r\n    return cases.filter((case_) => case_.status === status);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header con botón de agregar caso */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n            Gestión de Casos\r\n          </h2>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            {cases.length} casos en total\r\n          </p>\r\n        </div>\r\n        <button\r\n          onClick={() => setShowAddCaseModal(true)}\r\n          className=\"flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors cursor-pointer\"\r\n        >\r\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\r\n          Agregar Caso\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        {/* Desktop: 3 columnas | Tablet: 2 columnas + scroll | Móvil: Tabs */}\r\n\r\n        {/* Navegación móvil con tabs (solo visible en móvil) */}\r\n        <div className=\"block sm:hidden mb-4\">\r\n          <div className=\"flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1\">\r\n            {columns.map((column, index) => {\r\n              const columnCases = getCasesByStatus(column.status);\r\n              return (\r\n                <button\r\n                  key={column.id}\r\n                  onClick={() => setActiveMobileTab(index)}\r\n                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${\r\n                    activeMobileTab === index\r\n                      ? \"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm\"\r\n                      : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200\"\r\n                  }`}\r\n                >\r\n                  <span>{column.title}</span>\r\n                  <span className={`text-xs px-2 py-0.5 rounded-full ${\r\n                    activeMobileTab === index\r\n                      ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\"\r\n                      : \"bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300\"\r\n                  }`}>\r\n                    {columnCases.length}\r\n                  </span>\r\n                </button>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Navegación con flechas */}\r\n          <div className=\"flex justify-between items-center mt-3\">\r\n            <button\r\n              onClick={() => setActiveMobileTab(Math.max(0, activeMobileTab - 1))}\r\n              disabled={activeMobileTab === 0}\r\n              className=\"flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              <ChevronLeftIcon className=\"h-4 w-4\" />\r\n              <span>Anterior</span>\r\n            </button>\r\n\r\n            <div className=\"flex space-x-1\">\r\n              {columns.map((_, index) => (\r\n                <div\r\n                  key={index}\r\n                  className={`w-2 h-2 rounded-full transition-all ${\r\n                    activeMobileTab === index\r\n                      ? \"bg-blue-500 dark:bg-blue-400\"\r\n                      : \"bg-gray-300 dark:bg-gray-600\"\r\n                  }`}\r\n                />\r\n              ))}\r\n            </div>\r\n\r\n            <button\r\n              onClick={() => setActiveMobileTab(Math.min(columns.length - 1, activeMobileTab + 1))}\r\n              disabled={activeMobileTab === columns.length - 1}\r\n              className=\"flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              <span>Siguiente</span>\r\n              <ChevronRightIcon className=\"h-4 w-4\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Layout responsivo */}\r\n        <div className=\"hidden sm:block\">\r\n          {/* Desktop: Grid de 3 columnas */}\r\n          <div className=\"hidden lg:grid lg:grid-cols-3 lg:gap-6\">\r\n            {columns.map((column) => {\r\n              const columnCases = getCasesByStatus(column.status);\r\n              return (\r\n                <div\r\n                  key={column.id}\r\n                  className=\"bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm hover:shadow-md transition-all duration-200 p-5\"\r\n                >\r\n                  <div className=\"flex items-center justify-between mb-5\">\r\n                    <h3 className=\"font-bold text-gray-900 dark:text-gray-100 text-lg\">\r\n                      {column.title}\r\n                    </h3>\r\n                    <span className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm\">\r\n                      {columnCases.length}\r\n                    </span>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50\">\r\n                    {columnCases.map((case_) => (\r\n                      <CaseCard key={case_.id} case={case_} />\r\n                    ))}\r\n                    {columnCases.length === 0 && (\r\n                      <div className=\"flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3\">\r\n                        <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\r\n                          <svg className=\"w-8 h-8 text-gray-300 dark:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                          </svg>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"font-medium\">No hay casos</p>\r\n                          <p className=\"text-xs\">Los casos aparecerán aquí</p>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Tablet: Scroll horizontal */}\r\n          <div className=\"lg:hidden overflow-x-auto pb-4\">\r\n            <div className=\"flex space-x-4 min-w-max\">\r\n              {columns.map((column) => {\r\n                const columnCases = getCasesByStatus(column.status);\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm hover:shadow-md transition-all duration-200 p-5 w-80 flex-shrink-0\"\r\n                  >\r\n                    <div className=\"flex items-center justify-between mb-5\">\r\n                      <h3 className=\"font-bold text-gray-900 dark:text-gray-100 text-lg\">\r\n                        {column.title}\r\n                      </h3>\r\n                      <span className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm\">\r\n                        {columnCases.length}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50\">\r\n                      {columnCases.map((case_) => (\r\n                        <CaseCard key={case_.id} case={case_} />\r\n                      ))}\r\n                      {columnCases.length === 0 && (\r\n                        <div className=\"flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3\">\r\n                          <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\r\n                            <svg className=\"w-8 h-8 text-gray-300 dark:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                            </svg>\r\n                          </div>\r\n                          <div>\r\n                            <p className=\"font-medium\">No hay casos</p>\r\n                            <p className=\"text-xs\">Los casos aparecerán aquí</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        {/* Vista móvil: Solo mostrar la columna activa */}\r\n        <div className=\"block sm:hidden\">\r\n          {(() => {\r\n            const activeColumn = columns[activeMobileTab];\r\n            const columnCases = getCasesByStatus(activeColumn.status);\r\n\r\n            return (\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm p-5\">\r\n                <div className=\"flex items-center justify-between mb-5\">\r\n                  <h3 className=\"font-bold text-gray-900 dark:text-gray-100 text-lg\">\r\n                    {activeColumn.title}\r\n                  </h3>\r\n                  <span className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm\">\r\n                    {columnCases.length}\r\n                  </span>\r\n                </div>\r\n\r\n                <div className=\"space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50\">\r\n                  {columnCases.map((case_) => (\r\n                    <CaseCard key={case_.id} case={case_} />\r\n                  ))}\r\n                  {columnCases.length === 0 && (\r\n                    <div className=\"flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3\">\r\n                      <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\r\n                        <svg className=\"w-8 h-8 text-gray-300 dark:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"font-medium\">No hay casos</p>\r\n                        <p className=\"text-xs\">Los casos aparecerán aquí</p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          })()}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modal para agregar caso */}\r\n      <AddCaseModal\r\n        isOpen={showAddCaseModal}\r\n        onClose={() => setShowAddCaseModal(false)}\r\n        onCaseAdded={handleCaseAdded}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AANA;;;;;;AAQA,MAAM,UAAU;IACd;QAAE,IAAI;QAAO,OAAO;QAAU,QAAQ;IAAe;IACrD;QAAE,IAAI;QAAe,OAAO;QAAY,QAAQ;IAAuB;IACvE;QAAE,IAAI;QAAU,OAAO;QAAY,QAAQ;IAAkB;CAC9D;AAIM,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,wBAAwB;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,0BAA0B;gBAC1B,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;gBAErC,gDAAgD;gBAChD,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,YAAY;gBAEjE,kEAAkE;gBAClE,MAAM,WAAW;uBAAI;uBAAe;iBAAU;gBAC9C,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,iDAAiD;QACjD,SAAS,CAAA,OAAQ;gBAAC;mBAAY;aAAK;QACnC,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK;IAClD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAE,WAAU;;oCACV,MAAM,MAAM;oCAAC;;;;;;;;;;;;;kCAGlB,8OAAC;wBACC,SAAS,IAAM,oBAAoB;wBACnC,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCAIb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;oCACpB,MAAM,cAAc,iBAAiB,OAAO,MAAM;oCAClD,qBACE,8OAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,0GAA0G,EACpH,oBAAoB,QAChB,yEACA,iFACJ;;0DAEF,8OAAC;0DAAM,OAAO,KAAK;;;;;;0DACnB,8OAAC;gDAAK,WAAW,CAAC,iCAAiC,EACjD,oBAAoB,QAChB,kEACA,iEACJ;0DACC,YAAY,MAAM;;;;;;;uCAdhB,OAAO,EAAE;;;;;gCAkBpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,mBAAmB,KAAK,GAAG,CAAC,GAAG,kBAAkB;wCAChE,UAAU,oBAAoB;wCAC9B,WAAU;;0DAEV,8OAAC,6NAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,GAAG,sBACf,8OAAC;gDAEC,WAAW,CAAC,oCAAoC,EAC9C,oBAAoB,QAChB,iCACA,gCACJ;+CALG;;;;;;;;;;kDAUX,8OAAC;wCACC,SAAS,IAAM,mBAAmB,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,GAAG,kBAAkB;wCACjF,UAAU,oBAAoB,QAAQ,MAAM,GAAG;wCAC/C,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;0DACN,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC;oCACZ,MAAM,cAAc,iBAAiB,OAAO,MAAM;oCAClD,qBACE,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAK,WAAU;kEACb,YAAY,MAAM;;;;;;;;;;;;0DAIvB,8OAAC;gDAAI,WAAU;;oDACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,8OAAC,wIAAA,CAAA,WAAQ;4DAAgB,MAAM;2DAAhB,MAAM,EAAE;;;;;oDAExB,YAAY,MAAM,KAAK,mBACtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;oEAA2C,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAClG,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAK,GAAE;;;;;;;;;;;;;;;;0EAG3E,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;uCAzB1B,OAAO,EAAE;;;;;gCAgCpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC;wCACZ,MAAM,cAAc,iBAAiB,OAAO,MAAM;wCAClD,qBACE,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,OAAO,KAAK;;;;;;sEAEf,8OAAC;4DAAK,WAAU;sEACb,YAAY,MAAM;;;;;;;;;;;;8DAIvB,8OAAC;oDAAI,WAAU;;wDACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,8OAAC,wIAAA,CAAA,WAAQ;gEAAgB,MAAM;+DAAhB,MAAM,EAAE;;;;;wDAExB,YAAY,MAAM,KAAK,mBACtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAA2C,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAClG,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAK,GAAE;;;;;;;;;;;;;;;;8EAG3E,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAc;;;;;;sFAC3B,8OAAC;4EAAE,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CAzB1B,OAAO,EAAE;;;;;oCAgCpB;;;;;;;;;;;;;;;;;kCAON,8OAAC;wBAAI,WAAU;kCACZ,CAAC;4BACA,MAAM,eAAe,OAAO,CAAC,gBAAgB;4BAC7C,MAAM,cAAc,iBAAiB,aAAa,MAAM;4BAExD,qBACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,aAAa,KAAK;;;;;;0DAErB,8OAAC;gDAAK,WAAU;0DACb,YAAY,MAAM;;;;;;;;;;;;kDAIvB,8OAAC;wCAAI,WAAU;;4CACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,8OAAC,wIAAA,CAAA,WAAQ;oDAAgB,MAAM;mDAAhB,MAAM,EAAE;;;;;4CAExB,YAAY,MAAM,KAAK,mBACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2C,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAClG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAK,GAAE;;;;;;;;;;;;;;;;kEAG3E,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,8OAAC;gEAAE,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOrC,CAAC;;;;;;;;;;;;0BAKL,8OAAC,6IAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,aAAa;;;;;;;;;;;;AAIrB", "debugId": null}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseFiltersPanel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { XMarkIcon } from \"@heroicons/react/24/outline\";\r\nimport { CaseFilters } from \"../../_lib/types\";\r\n\r\ninterface CaseFiltersPanelProps {\r\n  filters: CaseFilters;\r\n  onFiltersChange: (filters: CaseFilters) => void;\r\n  onClose: () => void;\r\n}\r\n\r\nconst caseTypes = [\r\n  { value: \"all\", label: \"Todos los tipos\" },\r\n  { value: \"Laboral\", label: \"Laboral\" },\r\n  { value: \"Civil\", label: \"Civil\" },\r\n  { value: \"Familia\", label: \"Familia\" },\r\n  { value: \"Penal\", label: \"Penal\" },\r\n  { value: \"Comercial\", label: \"Comercial\" },\r\n];\r\n\r\nconst locations = [\r\n  { value: \"all\", label: \"Todas las ubicaciones\" },\r\n  { value: \"CABA\", label: \"CABA\" },\r\n  { value: \"Buenos Aires\", label: \"Buenos Aires\" },\r\n  { value: \"Córdoba\", label: \"Córdoba\" },\r\n  { value: \"Santa Fe\", label: \"Santa Fe\" },\r\n  { value: \"Mendoza\", label: \"Mendoza\" },\r\n  { value: \"Tucum<PERSON>\", label: \"Tucumán\" },\r\n  { value: \"Salta\", label: \"Salta\" },\r\n];\r\n\r\nconst budgetRanges = [\r\n  { value: \"all\", label: \"Todos los presupuestos\" },\r\n  { value: \"0-100000\", label: \"Hasta ARS 100,000\" },\r\n  { value: \"100000-250000\", label: \"ARS 100,000 - 250,000\" },\r\n  { value: \"250000-500000\", label: \"ARS 250,000 - 500,000\" },\r\n  { value: \"500000-999999999\", label: \"Más de ARS 500,000\" },\r\n];\r\n\r\nconst urgencyLevels = [\r\n  { value: \"all\", label: \"Todas las urgencias\" },\r\n  { value: \"urgent\", label: \"Urgente\" },\r\n  { value: \"high\", label: \"Alta\" },\r\n  { value: \"medium\", label: \"Media\" },\r\n  { value: \"low\", label: \"Baja\" },\r\n];\r\n\r\nconst sortOptions = [\r\n  { value: \"newest\", label: \"Más recientes\" },\r\n  { value: \"budget_high\", label: \"Mayor presupuesto\" },\r\n  { value: \"budget_low\", label: \"Menor presupuesto\" },\r\n  { value: \"bids_count\", label: \"Menos propuestas\" },\r\n];\r\n\r\nexport function CaseFiltersPanel({\r\n  filters,\r\n  onFiltersChange,\r\n  onClose,\r\n}: CaseFiltersPanelProps) {\r\n  const handleFilterChange = (key: keyof CaseFilters, value: string) => {\r\n    onFiltersChange({\r\n      ...filters,\r\n      [key]: value,\r\n    });\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    onFiltersChange({\r\n      type: \"all\",\r\n      location: \"all\",\r\n      budgetRange: \"all\",\r\n      urgencyLevel: \"all\",\r\n      sortBy: \"newest\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n          Filtros de Búsqueda\r\n        </h3>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"text-gray-400 cursor-pointer dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\r\n        >\r\n          <XMarkIcon className=\"h-5 w-5\" />\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\r\n        {/* Case Type */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Tipo de Caso\r\n          </label>\r\n          <select\r\n            value={filters.type}\r\n            onChange={(e) => handleFilterChange(\"type\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {caseTypes.map((type) => (\r\n              <option key={type.value} value={type.value}>\r\n                {type.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Location */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Ubicación\r\n          </label>\r\n          <select\r\n            value={filters.location}\r\n            onChange={(e) => handleFilterChange(\"location\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {locations.map((location) => (\r\n              <option key={location.value} value={location.value}>\r\n                {location.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Budget Range */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Presupuesto\r\n          </label>\r\n          <select\r\n            value={filters.budgetRange}\r\n            onChange={(e) => handleFilterChange(\"budgetRange\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {budgetRanges.map((range) => (\r\n              <option key={range.value} value={range.value}>\r\n                {range.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Urgency Level */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Urgencia\r\n          </label>\r\n          <select\r\n            value={filters.urgencyLevel}\r\n            onChange={(e) => handleFilterChange(\"urgencyLevel\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {urgencyLevels.map((level) => (\r\n              <option key={level.value} value={level.value}>\r\n                {level.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Sort By */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Ordenar por\r\n          </label>\r\n          <select\r\n            value={filters.sortBy}\r\n            onChange={(e) => handleFilterChange(\"sortBy\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {sortOptions.map((option) => (\r\n              <option key={option.value} value={option.value}>\r\n                {option.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-between mt-6\">\r\n        <button\r\n          onClick={clearFilters}\r\n          className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n        >\r\n          Limpiar Filtros\r\n        </button>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n        >\r\n          Aplicar Filtros\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,OAAO;IAAwB;IAC/C;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAO,OAAO;IAAyB;IAChD;QAAE,OAAO;QAAY,OAAO;IAAoB;IAChD;QAAE,OAAO;QAAiB,OAAO;IAAwB;IACzD;QAAE,OAAO;QAAiB,OAAO;IAAwB;IACzD;QAAE,OAAO;QAAoB,OAAO;IAAqB;CAC1D;AAED,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAO,OAAO;IAAsB;IAC7C;QAAE,OAAO;QAAU,OAAO;IAAU;IACpC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAQ;IAClC;QAAE,OAAO;QAAO,OAAO;IAAO;CAC/B;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAU,OAAO;IAAgB;IAC1C;QAAE,OAAO;QAAe,OAAO;IAAoB;IACnD;QAAE,OAAO;QAAc,OAAO;IAAoB;IAClD;QAAE,OAAO;QAAc,OAAO;IAAmB;CAClD;AAEM,SAAS,iBAAiB,EAC/B,OAAO,EACP,eAAe,EACf,OAAO,EACe;IACtB,MAAM,qBAAqB,CAAC,KAAwB;QAClD,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,MAAM;YACN,UAAU;YACV,aAAa;YACb,cAAc;YACd,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,QAAQ,IAAI;gCACnB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;0CAET,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wCAAwB,OAAO,KAAK,KAAK;kDACvC,KAAK,KAAK;uCADA,KAAK,KAAK;;;;;;;;;;;;;;;;kCAQ7B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,QAAQ,QAAQ;gCACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC9D,WAAU;0CAET,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wCAA4B,OAAO,SAAS,KAAK;kDAC/C,SAAS,KAAK;uCADJ,SAAS,KAAK;;;;;;;;;;;;;;;;kCAQjC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,QAAQ,WAAW;gCAC1B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gCACjE,WAAU;0CAET,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;wCAAyB,OAAO,MAAM,KAAK;kDACzC,MAAM,KAAK;uCADD,MAAM,KAAK;;;;;;;;;;;;;;;;kCAQ9B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,QAAQ,YAAY;gCAC3B,UAAU,CAAC,IAAM,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAClE,WAAU;0CAET,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;wCAAyB,OAAO,MAAM,KAAK;kDACzC,MAAM,KAAK;uCADD,MAAM,KAAK;;;;;;;;;;;;;;;;kCAQ9B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAQjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 2195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/BidModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { XMarkIcon, CheckCircleIcon, ChevronDownIcon } from \"@heroicons/react/24/outline\";\r\nimport * as Dialog from \"@radix-ui/react-dialog\";\r\nimport { AvailableCase, CaseBid } from \"../../_lib/types\";\r\n\r\ninterface BidModalProps {\r\n  case: AvailableCase;\r\n  onClose: () => void;\r\n  onSubmit: (bidData: Partial<CaseBid>) => void;\r\n}\r\n\r\nexport function BidModal({ case: caseData, onClose, onSubmit }: BidModalProps) {\r\n  const [step, setStep] = useState<\"form\" | \"review\" | \"success\">(\"form\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    feeType: \"fixed\",\r\n    feeAmount: \"\",\r\n    feeCurrency: \"ARS\",\r\n    feePercentage: \"\",\r\n    feeDescription: \"\",\r\n    estimatedTimeline: \"\",\r\n    experience: \"\",\r\n    availability: \"immediate\",\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  const validateForm = (): boolean => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    // Validate fee amount or percentage based on fee type\r\n    if (formData.feeType !== \"contingency\") {\r\n      if (!formData.feeAmount.trim()) {\r\n        newErrors.feeAmount = \"El monto es obligatorio\";\r\n      } else if (isNaN(Number(formData.feeAmount)) || Number(formData.feeAmount) <= 0) {\r\n        newErrors.feeAmount = \"Ingrese un monto válido mayor a 0\";\r\n      }\r\n    } else {\r\n      if (!formData.feePercentage.trim()) {\r\n        newErrors.feePercentage = \"El porcentaje es obligatorio\";\r\n      } else if (isNaN(Number(formData.feePercentage)) || Number(formData.feePercentage) <= 0 || Number(formData.feePercentage) > 50) {\r\n        newErrors.feePercentage = \"Ingrese un porcentaje válido entre 1 y 50\";\r\n      }\r\n    }\r\n\r\n    // Validate fee description\r\n    if (!formData.feeDescription.trim()) {\r\n      newErrors.feeDescription = \"La descripción de honorarios es obligatoria\";\r\n    }\r\n\r\n    // Validate estimated timeline\r\n    if (!formData.estimatedTimeline.trim()) {\r\n      newErrors.estimatedTimeline = \"El tiempo estimado es obligatorio\";\r\n    }\r\n\r\n    // Validate experience\r\n    if (!formData.experience.trim()) {\r\n      newErrors.experience = \"La experiencia relevante es obligatoria\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!validateForm()) return;\r\n    setStep(\"review\");\r\n  };\r\n\r\n  const handleConfirmSubmit = async () => {\r\n    setIsSubmitting(true);\r\n\r\n    // Simulate submission delay\r\n    setTimeout(() => {\r\n      const bidData = {\r\n        proposedFee: {\r\n          type: formData.feeType as \"fixed\" | \"contingency\" | \"hourly\",\r\n          amount:\r\n            formData.feeType !== \"contingency\"\r\n              ? parseInt(formData.feeAmount)\r\n              : undefined,\r\n          currency:\r\n            formData.feeType !== \"contingency\"\r\n              ? (formData.feeCurrency as \"ARS\" | \"USD\" | \"UMA\")\r\n              : undefined,\r\n          percentage:\r\n            formData.feeType === \"contingency\"\r\n              ? parseInt(formData.feePercentage)\r\n              : undefined,\r\n          description: formData.feeDescription,\r\n        },\r\n        estimatedTimeline: formData.estimatedTimeline,\r\n        experience: formData.experience,\r\n        availability: formData.availability,\r\n      };\r\n\r\n      onSubmit(bidData);\r\n      setIsSubmitting(false);\r\n      setStep(\"success\");\r\n\r\n      // Auto-close after success\r\n      setTimeout(() => {\r\n        handleClose();\r\n      }, 2000);\r\n    }, 1500);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    // Reset form and errors\r\n    setFormData({\r\n      feeType: \"fixed\",\r\n      feeAmount: \"\",\r\n      feeCurrency: \"ARS\",\r\n      feePercentage: \"\",\r\n      feeDescription: \"\",\r\n      estimatedTimeline: \"\",\r\n      experience: \"\",\r\n      availability: \"immediate\",\r\n    });\r\n    setStep(\"form\");\r\n    setErrors({});\r\n    setIsSubmitting(false);\r\n    onClose();\r\n  };\r\n\r\n  const renderStepContent = () => {\r\n    switch (step) {\r\n      case \"form\":\r\n        return (\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* Case Summary */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n              <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                {caseData.title}\r\n              </h4>\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\r\n                <p>\r\n                  <strong>Tipo:</strong> {caseData.type}\r\n                </p>\r\n                <p>\r\n                  <strong>Cliente:</strong> {caseData.clientName}\r\n                </p>\r\n                <p>\r\n                  <strong>Ubicación:</strong> {caseData.clientLocation}\r\n                </p>\r\n                <p>\r\n                  <strong>Presupuesto:</strong> {caseData.budgetRange}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Fee Structure */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n                Estructura de Honorarios\r\n              </label>\r\n              <div className=\"space-y-3\">\r\n                <label className=\"flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"feeType\"\r\n                    value=\"fixed\"\r\n                    checked={formData.feeType === \"fixed\"}\r\n                    onChange={(e) =>\r\n                      setFormData((prev) => ({\r\n                        ...prev,\r\n                        feeType: e.target.value,\r\n                      }))\r\n                    }\r\n                    className=\"text-blue-600 focus:ring-blue-500\"\r\n                  />\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                      Honorario Fijo\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                      Monto total por el caso completo\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n\r\n                <label className=\"flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"feeType\"\r\n                    value=\"hourly\"\r\n                    checked={formData.feeType === \"hourly\"}\r\n                    onChange={(e) =>\r\n                      setFormData((prev) => ({\r\n                        ...prev,\r\n                        feeType: e.target.value,\r\n                      }))\r\n                    }\r\n                    className=\"text-blue-600 focus:ring-blue-500\"\r\n                  />\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium text-gray-900 dark:text-gray-100\">Por Hora</div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                      Tarifa por hora trabajada\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n\r\n                <label className=\"flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"feeType\"\r\n                    value=\"contingency\"\r\n                    checked={formData.feeType === \"contingency\"}\r\n                    onChange={(e) =>\r\n                      setFormData((prev) => ({\r\n                        ...prev,\r\n                        feeType: e.target.value,\r\n                      }))\r\n                    }\r\n                    className=\"text-blue-600 focus:ring-blue-500\"\r\n                  />\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium text-gray-900 dark:text-gray-100\">Cuota Litis</div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                      Porcentaje del resultado obtenido\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Fee Amount */}\r\n            {formData.feeType !== \"contingency\" ? (\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Monto {formData.feeType === \"hourly\" ? \"por Hora\" : \"Total\"} *\r\n                </label>\r\n                <div className=\"flex space-x-2 items-center\">\r\n                  <div className=\"flex-1\">\r\n                    <input\r\n                      type=\"number\"\r\n                      value={formData.feeAmount}\r\n                      onChange={(e) =>\r\n                        setFormData((prev) => ({\r\n                          ...prev,\r\n                          feeAmount: e.target.value,\r\n                        }))\r\n                      }\r\n                      className={`w-full h-10 px-3 py-2 border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\r\n                        errors.feeAmount ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\r\n                      }`}\r\n                      placeholder=\"150000\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"w-24 relative\">\r\n                    <select\r\n                      value={formData.feeCurrency}\r\n                      onChange={(e) =>\r\n                        setFormData((prev) => ({\r\n                          ...prev,\r\n                          feeCurrency: e.target.value,\r\n                        }))\r\n                      }\r\n                      className=\"w-full h-10 px-3 pr-8 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer\"\r\n                    >\r\n                      <option value=\"ARS\">ARS</option>\r\n                      <option value=\"USD\">USD</option>\r\n                      <option value=\"UMA\">UMA</option>\r\n                    </select>\r\n                    <ChevronDownIcon className=\"absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500 pointer-events-none\" />\r\n                  </div>\r\n                </div>\r\n                {errors.feeAmount && (\r\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.feeAmount}</p>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Porcentaje (%) *\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max=\"50\"\r\n                  value={formData.feePercentage}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) => ({\r\n                      ...prev,\r\n                      feePercentage: e.target.value,\r\n                    }))\r\n                  }\r\n                  className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\r\n                    errors.feePercentage ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\r\n                  }`}\r\n                  placeholder=\"30\"\r\n                />\r\n                {errors.feePercentage && (\r\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.feePercentage}</p>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Fee Description */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Descripción de Honorarios *\r\n              </label>\r\n              <textarea\r\n                rows={3}\r\n                value={formData.feeDescription}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({\r\n                    ...prev,\r\n                    feeDescription: e.target.value,\r\n                  }))\r\n                }\r\n                className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${\r\n                  errors.feeDescription ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\r\n                }`}\r\n                placeholder=\"Incluye todas las gestiones necesarias, presentaciones ante organismos, etc.\"\r\n              />\r\n              {errors.feeDescription && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.feeDescription}</p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Timeline */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Tiempo Estimado de Resolución *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                value={formData.estimatedTimeline}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({\r\n                    ...prev,\r\n                    estimatedTimeline: e.target.value,\r\n                  }))\r\n                }\r\n                className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\r\n                  errors.estimatedTimeline ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\r\n                }`}\r\n                placeholder=\"3-6 meses\"\r\n              />\r\n              {errors.estimatedTimeline && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.estimatedTimeline}</p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Experience */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Experiencia Relevante *\r\n              </label>\r\n              <textarea\r\n                rows={4}\r\n                value={formData.experience}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({\r\n                    ...prev,\r\n                    experience: e.target.value,\r\n                  }))\r\n                }\r\n                className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${\r\n                  errors.experience ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\r\n                }`}\r\n                placeholder=\"Describa su experiencia en casos similares, resultados obtenidos, especialización en el área, etc.\"\r\n              />\r\n              {errors.experience && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.experience}</p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Availability */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Disponibilidad para Comenzar\r\n              </label>\r\n              <select\r\n                value={formData.availability}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({\r\n                    ...prev,\r\n                    availability: e.target.value,\r\n                  }))\r\n                }\r\n                className=\"w-full cursor-pointer px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n              >\r\n                <option value=\"immediate\">Inmediata</option>\r\n                <option value=\"1_week\">En 1 semana</option>\r\n                <option value=\"2_weeks\">En 2 semanas</option>\r\n                <option value=\"1_month\">En 1 mes</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3 pt-4\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleClose}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer\"\r\n              >\r\n                Cancelar\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer\"\r\n              >\r\n                Revisar Propuesta\r\n              </button>\r\n            </div>\r\n          </form>\r\n        );\r\n\r\n      case \"review\":\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                Revisar Propuesta\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                Verifique los detalles antes de enviar su propuesta\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3\">\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"font-medium text-gray-900 dark:text-gray-100\">Honorarios:</span>\r\n                <span className=\"text-gray-600 dark:text-gray-300\">\r\n                  {formData.feeType === \"contingency\"\r\n                    ? `${formData.feePercentage}% del resultado`\r\n                    : `${formData.feeCurrency} ${parseInt(formData.feeAmount).toLocaleString(\r\n                        \"es-AR\"\r\n                      )} ${\r\n                        formData.feeType === \"hourly\" ? \"por hora\" : \"total\"\r\n                      }`}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                  Tiempo estimado:\r\n                </span>\r\n                <span className=\"text-gray-600 dark:text-gray-300\">\r\n                  {formData.estimatedTimeline}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                  Disponibilidad:\r\n                </span>\r\n                <span className=\"text-gray-600 dark:text-gray-300\">\r\n                  {formData.availability === \"immediate\"\r\n                    ? \"Inmediata\"\r\n                    : formData.availability === \"1_week\"\r\n                    ? \"En 1 semana\"\r\n                    : formData.availability === \"2_weeks\"\r\n                    ? \"En 2 semanas\"\r\n                    : \"En 1 mes\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4\">\r\n              <p className=\"text-sm text-blue-800 dark:text-blue-200\">\r\n                <strong>Nota:</strong> Una vez enviada, su propuesta será\r\n                revisada por el cliente. Recibirá una notificación cuando el\r\n                cliente tome una decisión.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                onClick={() => setStep(\"form\")}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer\"\r\n              >\r\n                Editar\r\n              </button>\r\n              <button\r\n                onClick={handleConfirmSubmit}\r\n                disabled={isSubmitting}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                    Enviando...\r\n                  </div>\r\n                ) : (\r\n                  \"Enviar Propuesta\"\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case \"success\":\r\n        return (\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"text-center space-y-6\"\r\n          >\r\n            <div className=\"text-green-600\">\r\n              <CheckCircleIcon className=\"h-16 w-16 mx-auto mb-4\" />\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                ¡Propuesta Enviada!\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                Su propuesta para &quot;{caseData.title}&quot; ha sido enviada\r\n                exitosamente. El cliente la revisará y se pondrá en contacto con\r\n                usted.\r\n              </p>\r\n            </div>\r\n          </motion.div>\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog.Root open onOpenChange={handleClose}>\r\n      <Dialog.Portal>\r\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 z-50\" />\r\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <Dialog.Title className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              {step === \"form\" && \"Enviar Propuesta\"}\r\n              {step === \"review\" && \"Revisar Propuesta\"}\r\n              {step === \"success\" && \"Propuesta Enviada\"}\r\n            </Dialog.Title>\r\n            {step !== \"success\" && (\r\n              <Dialog.Close asChild>\r\n                <button\r\n                  onClick={handleClose}\r\n                  className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\"\r\n                >\r\n                  <XMarkIcon className=\"h-5 w-5\" />\r\n                </button>\r\n              </Dialog.Close>\r\n            )}\r\n          </div>\r\n\r\n          {renderStepContent()}\r\n        </Dialog.Content>\r\n      </Dialog.Portal>\r\n    </Dialog.Root>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAcO,SAAS,SAAS,EAAE,MAAM,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAiB;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;QACT,WAAW;QACX,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,mBAAmB;QACnB,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,sDAAsD;QACtD,IAAI,SAAS,OAAO,KAAK,eAAe;YACtC,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;gBAC9B,UAAU,SAAS,GAAG;YACxB,OAAO,IAAI,MAAM,OAAO,SAAS,SAAS,MAAM,OAAO,SAAS,SAAS,KAAK,GAAG;gBAC/E,UAAU,SAAS,GAAG;YACxB;QACF,OAAO;YACL,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;gBAClC,UAAU,aAAa,GAAG;YAC5B,OAAO,IAAI,MAAM,OAAO,SAAS,aAAa,MAAM,OAAO,SAAS,aAAa,KAAK,KAAK,OAAO,SAAS,aAAa,IAAI,IAAI;gBAC9H,UAAU,aAAa,GAAG;YAC5B;QACF;QAEA,2BAA2B;QAC3B,IAAI,CAAC,SAAS,cAAc,CAAC,IAAI,IAAI;YACnC,UAAU,cAAc,GAAG;QAC7B;QAEA,8BAA8B;QAC9B,IAAI,CAAC,SAAS,iBAAiB,CAAC,IAAI,IAAI;YACtC,UAAU,iBAAiB,GAAG;QAChC;QAEA,sBAAsB;QACtB,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;YAC/B,UAAU,UAAU,GAAG;QACzB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,gBAAgB;QACrB,QAAQ;IACV;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;QAEhB,4BAA4B;QAC5B,WAAW;YACT,MAAM,UAAU;gBACd,aAAa;oBACX,MAAM,SAAS,OAAO;oBACtB,QACE,SAAS,OAAO,KAAK,gBACjB,SAAS,SAAS,SAAS,IAC3B;oBACN,UACE,SAAS,OAAO,KAAK,gBAChB,SAAS,WAAW,GACrB;oBACN,YACE,SAAS,OAAO,KAAK,gBACjB,SAAS,SAAS,aAAa,IAC/B;oBACN,aAAa,SAAS,cAAc;gBACtC;gBACA,mBAAmB,SAAS,iBAAiB;gBAC7C,YAAY,SAAS,UAAU;gBAC/B,cAAc,SAAS,YAAY;YACrC;YAEA,SAAS;YACT,gBAAgB;YAChB,QAAQ;YAER,2BAA2B;YAC3B,WAAW;gBACT;YACF,GAAG;QACL,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,wBAAwB;QACxB,YAAY;YACV,SAAS;YACT,WAAW;YACX,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,mBAAmB;YACnB,YAAY;YACZ,cAAc;QAChB;QACA,QAAQ;QACR,UAAU,CAAC;QACX,gBAAgB;QAChB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,SAAS,IAAI;;;;;;;sDAEvC,8OAAC;;8DACC,8OAAC;8DAAO;;;;;;gDAAiB;gDAAE,SAAS,UAAU;;;;;;;sDAEhD,8OAAC;;8DACC,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE,SAAS,cAAc;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,SAAS,WAAW;;;;;;;;;;;;;;;;;;;sCAMzD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,SAAS,OAAO,KAAK;oDAC9B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gEACrB,GAAG,IAAI;gEACP,SAAS,EAAE,MAAM,CAAC,KAAK;4DACzB,CAAC;oDAEH,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAG9D,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAM9D,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,SAAS,OAAO,KAAK;oDAC9B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gEACrB,GAAG,IAAI;gEACP,SAAS,EAAE,MAAM,CAAC,KAAK;4DACzB,CAAC;oDAEH,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAM9D,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,SAAS,OAAO,KAAK;oDAC9B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gEACrB,GAAG,IAAI;gEACP,SAAS,EAAE,MAAM,CAAC,KAAK;4DACzB,CAAC;oDAEH,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASjE,SAAS,OAAO,KAAK,8BACpB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;;wCAAkE;wCAC1E,SAAS,OAAO,KAAK,WAAW,aAAa;wCAAQ;;;;;;;8CAE9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4DACrB,GAAG,IAAI;4DACP,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC3B,CAAC;gDAEH,WAAW,CAAC,gLAAgL,EAC1L,OAAO,SAAS,GAAG,mBAAmB,wCACtC;gDACF,aAAY;;;;;;;;;;;sDAGhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gEACrB,GAAG,IAAI;gEACP,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC7B,CAAC;oDAEH,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAM;;;;;;;;;;;;8DAEtB,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;;;;;;;;;;;;;gCAG9B,OAAO,SAAS,kBACf,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,SAAS;;;;;;;;;;;iDAIhF,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC/B,CAAC;oCAEH,WAAW,CAAC,2KAA2K,EACrL,OAAO,aAAa,GAAG,mBAAmB,wCAC1C;oCACF,aAAY;;;;;;gCAEb,OAAO,aAAa,kBACnB,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,aAAa;;;;;;;;;;;;sCAMtF,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAM;oCACN,OAAO,SAAS,cAAc;oCAC9B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAChC,CAAC;oCAEH,WAAW,CAAC,uLAAuL,EACjM,OAAO,cAAc,GAAG,mBAAmB,wCAC3C;oCACF,aAAY;;;;;;gCAEb,OAAO,cAAc,kBACpB,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,cAAc;;;;;;;;;;;;sCAKrF,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,iBAAiB;oCACjC,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CACnC,CAAC;oCAEH,WAAW,CAAC,2KAA2K,EACrL,OAAO,iBAAiB,GAAG,mBAAmB,wCAC9C;oCACF,aAAY;;;;;;gCAEb,OAAO,iBAAiB,kBACvB,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,iBAAiB;;;;;;;;;;;;sCAKxF,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAM;oCACN,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC5B,CAAC;oCAEH,WAAW,CAAC,uLAAuL,EACjM,OAAO,UAAU,GAAG,mBAAmB,wCACvC;oCACF,aAAY;;;;;;gCAEb,OAAO,UAAU,kBAChB,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,UAAU;;;;;;;;;;;;sCAKjF,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC9B,CAAC;oCAEH,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;;;;;;;sCAI5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAOT,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAK1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;sDAC/D,8OAAC;4CAAK,WAAU;sDACb,SAAS,OAAO,KAAK,gBAClB,GAAG,SAAS,aAAa,CAAC,eAAe,CAAC,GAC1C,GAAG,SAAS,WAAW,CAAC,CAAC,EAAE,SAAS,SAAS,SAAS,EAAE,cAAc,CACpE,SACA,CAAC,EACD,SAAS,OAAO,KAAK,WAAW,aAAa,SAC7C;;;;;;;;;;;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;sDAG/D,8OAAC;4CAAK,WAAU;sDACb,SAAS,iBAAiB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;sDAG/D,8OAAC;4CAAK,WAAU;sDACb,SAAS,YAAY,KAAK,cACvB,cACA,SAAS,YAAY,KAAK,WAC1B,gBACA,SAAS,YAAY,KAAK,YAC1B,iBACA;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAAc;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,QAAQ;oCACvB,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;;YAOZ,KAAK;gBACH,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAE7B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAE,WAAU;;wCAA2C;wCAC7B,SAAS,KAAK;wCAAC;;;;;;;;;;;;;;;;;;;YAQlD;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,kKAAA,CAAA,OAAW;QAAC,IAAI;QAAC,cAAc;kBAC9B,cAAA,8OAAC,kKAAA,CAAA,SAAa;;8BACZ,8OAAC,kKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,8OAAC,kKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kKAAA,CAAA,QAAY;oCAAC,WAAU;;wCACrB,SAAS,UAAU;wCACnB,SAAS,YAAY;wCACrB,SAAS,aAAa;;;;;;;gCAExB,SAAS,2BACR,8OAAC,kKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wBAM5B;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 3274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/AvailableCases.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  MagnifyingGlassIcon,\r\n  FunnelIcon,\r\n  ClockIcon,\r\n  MapPinIcon,\r\n  CurrencyDollarIcon,\r\n  UserGroupIcon,\r\n  ExclamationTriangleIcon,\r\n  CheckCircleIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { formatDistanceToNow } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport { AvailableCase, CaseFilters, CaseBid } from \"../../_lib/types\";\r\nimport { CaseFiltersPanel } from \"./CaseFiltersPanel\";\r\nimport { BidModal } from \"../modals/BidModal\";\r\nimport { useIsMounted } from \"../../_lib/useIsomorphicDate\";\r\n\r\nexport function AvailableCases() {\r\n  const [availableCases, setAvailableCases] = useState<AvailableCase[]>([]);\r\n  const [filteredCases, setFilteredCases] = useState<AvailableCase[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [showFilters, setShowFilters] = useState(false);\r\n  const [selectedCase, setSelectedCase] = useState<AvailableCase | null>(null);\r\n  const [showBidModal, setShowBidModal] = useState(false);\r\n  const [filters, setFilters] = useState<CaseFilters>({\r\n    type: \"all\",\r\n    location: \"all\",\r\n    budgetRange: \"all\",\r\n    urgencyLevel: \"all\",\r\n    sortBy: \"newest\",\r\n  });\r\n  const isMounted = useIsMounted();\r\n\r\n  // Load available cases\r\n  useEffect(() => {\r\n    const loadCases = async () => {\r\n      try {\r\n        const response = await fetch(\"/data/available-cases.json\");\r\n        const casesData = await response.json();\r\n        setAvailableCases(casesData);\r\n        setFilteredCases(casesData);\r\n      } catch (error) {\r\n        console.error(\"Error loading available cases:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadCases();\r\n  }, []);\r\n\r\n  // Apply filters and search\r\n  useEffect(() => {\r\n    let filtered = [...availableCases];\r\n\r\n    // Search filter\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(\r\n        (case_) =>\r\n          case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n          case_.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n          case_.type.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Type filter\r\n    if (filters.type !== \"all\") {\r\n      filtered = filtered.filter((case_) => case_.type === filters.type);\r\n    }\r\n\r\n    // Location filter\r\n    if (filters.location !== \"all\") {\r\n      filtered = filtered.filter((case_) =>\r\n        case_.clientLocation\r\n          .toLowerCase()\r\n          .includes(filters.location.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Budget range filter\r\n    if (filters.budgetRange !== \"all\") {\r\n      const [min, max] = filters.budgetRange.split(\"-\").map(Number);\r\n      filtered = filtered.filter(\r\n        (case_) => case_.estimatedValue >= min && case_.estimatedValue <= max\r\n      );\r\n    }\r\n\r\n    // Urgency filter\r\n    if (filters.urgencyLevel !== \"all\") {\r\n      filtered = filtered.filter(\r\n        (case_) => case_.urgencyLevel === filters.urgencyLevel\r\n      );\r\n    }\r\n\r\n    // Sorting\r\n    switch (filters.sortBy) {\r\n      case \"newest\":\r\n        filtered.sort(\r\n          (a, b) =>\r\n            new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime()\r\n        );\r\n        break;\r\n      case \"budget_high\":\r\n        filtered.sort((a, b) => b.estimatedValue - a.estimatedValue);\r\n        break;\r\n      case \"budget_low\":\r\n        filtered.sort((a, b) => a.estimatedValue - b.estimatedValue);\r\n        break;\r\n      case \"bids_count\":\r\n        filtered.sort((a, b) => a.bidsCount - b.bidsCount);\r\n        break;\r\n    }\r\n\r\n    setFilteredCases(filtered);\r\n  }, [availableCases, searchTerm, filters]);\r\n\r\n  const handleBidSubmit = (caseId: string, bidData: Partial<CaseBid>) => {\r\n    // In a real app, this would submit to the backend\r\n    console.log(\"Bid submitted for case:\", caseId, bidData);\r\n\r\n    // Update local state to show bid was submitted\r\n    const userBids = JSON.parse(localStorage.getItem(\"userBids\") || \"[]\");\r\n    const newBid = {\r\n      id: `bid-${Date.now()}`,\r\n      caseId,\r\n      ...bidData,\r\n      submittedAt: new Date().toISOString(),\r\n      status: \"submitted\",\r\n    };\r\n    userBids.push(newBid);\r\n    localStorage.setItem(\"userBids\", JSON.stringify(userBids));\r\n\r\n    setShowBidModal(false);\r\n    setSelectedCase(null);\r\n  };\r\n\r\n  const getUrgencyColor = (urgency: string) => {\r\n    switch (urgency) {\r\n      case \"urgent\":\r\n        return \"text-red-600 bg-red-100\";\r\n      case \"high\":\r\n        return \"text-orange-600 bg-orange-100\";\r\n      case \"medium\":\r\n        return \"text-yellow-600 bg-yellow-100\";\r\n      case \"low\":\r\n        return \"text-green-600 bg-green-100\";\r\n      default:\r\n        return \"text-gray-600 bg-gray-100\";\r\n    }\r\n  };\r\n\r\n  const getUrgencyIcon = (urgency: string) => {\r\n    switch (urgency) {\r\n      case \"urgent\":\r\n        return ExclamationTriangleIcon;\r\n      case \"high\":\r\n        return ClockIcon;\r\n      case \"medium\":\r\n        return ClockIcon;\r\n      case \"low\":\r\n        return CheckCircleIcon;\r\n      default:\r\n        return ClockIcon;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n            Casos Disponibles\r\n          </h2>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            {filteredCases.length} casos disponibles para aplicar\r\n          </p>\r\n        </div>\r\n\r\n        {/* Quick stats */}\r\n        <div className=\"flex items-center space-x-4 text-sm\">\r\n          <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\r\n            <ExclamationTriangleIcon className=\"h-4 w-4\" />\r\n            <span>\r\n              {availableCases.filter((c) => c.urgencyLevel === \"urgent\").length}{\" \"}\r\n              urgentes\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center space-x-1 text-blue-600 dark:text-blue-400\">\r\n            <CurrencyDollarIcon className=\"h-4 w-4\" />\r\n            <span>\r\n              ARS{\" \"}\r\n              {Math.round(\r\n                availableCases.reduce((sum, c) => sum + c.estimatedValue, 0) /\r\n                  1000\r\n              )}\r\n              K total\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Search and Filters */}\r\n      <div className=\"flex flex-col lg:flex-row gap-4\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"relative\">\r\n            <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n            <input\r\n              type=\"text\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              placeholder=\"Buscar casos por título, descripción o tipo...\"\r\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <button\r\n          onClick={() => setShowFilters(!showFilters)}\r\n          className=\"inline-flex items-center cursor-pointer px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors\"\r\n        >\r\n          <FunnelIcon className=\"h-4 w-4 mr-2\" />\r\n          Filtros\r\n        </button>\r\n      </div>\r\n\r\n      {/* Filters Panel */}\r\n      {showFilters && (\r\n        <CaseFiltersPanel\r\n          filters={filters}\r\n          onFiltersChange={setFilters}\r\n          onClose={() => setShowFilters(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Cases Grid */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {filteredCases.map((case_, index) => {\r\n          const UrgencyIcon = getUrgencyIcon(case_.urgencyLevel);\r\n          const userBids = JSON.parse(localStorage.getItem(\"userBids\") || \"[]\");\r\n          const hasBid = userBids.some(\r\n            (bid: { caseId: string }) => bid.caseId === case_.id\r\n          );\r\n\r\n          return (\r\n            <motion.div\r\n              key={case_.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\"\r\n            >\r\n              {/* Header */}\r\n              <div className=\"flex items-start justify-between mb-4\">\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                    {case_.title}\r\n                  </h3>\r\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300\">\r\n                    <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\">\r\n                      {case_.type}\r\n                    </span>\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <MapPinIcon className=\"h-3 w-3\" />\r\n                      <span>{case_.clientLocation}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(\r\n                    case_.urgencyLevel\r\n                  )}`}\r\n                >\r\n                  <UrgencyIcon className=\"h-3 w-3 mr-1\" />\r\n                  {case_.urgencyLevel === \"urgent\"\r\n                    ? \"Urgente\"\r\n                    : case_.urgencyLevel === \"high\"\r\n                    ? \"Alta\"\r\n                    : case_.urgencyLevel === \"medium\"\r\n                    ? \"Media\"\r\n                    : \"Baja\"}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\r\n                {case_.description}\r\n              </p>\r\n\r\n              {/* Details */}\r\n              <div className=\"grid grid-cols-2 gap-4 mb-4 text-sm\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <CurrencyDollarIcon className=\"h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-gray-600 dark:text-gray-300\">\r\n                    {case_.budgetRange}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <UserGroupIcon className=\"h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-gray-600 dark:text-gray-300\">\r\n                    {case_.bidsCount} propuestas\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <ClockIcon className=\"h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-gray-600 dark:text-gray-300\">\r\n                    {isMounted\r\n                      ? formatDistanceToNow(new Date(case_.postedAt), {\r\n                          addSuffix: true,\r\n                          locale: es,\r\n                        })\r\n                      : \"Hace poco\"}\r\n                  </span>\r\n                </div>\r\n                <div className=\"text-gray-600 dark:text-gray-300\">\r\n                  Cliente: {case_.clientName}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Actions */}\r\n              <div className=\"flex space-x-3\">\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedCase(case_);\r\n                    setShowBidModal(true);\r\n                  }}\r\n                  disabled={hasBid}\r\n                  className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${\r\n                    hasBid\r\n                      ? \"bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed\"\r\n                      : \"bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 cursor-pointer\"\r\n                  }`}\r\n                >\r\n                  {hasBid ? \"Propuesta Enviada\" : \"Enviar Propuesta\"}\r\n                </button>\r\n                <button className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\">\r\n                  Ver Detalles\r\n                </button>\r\n              </div>\r\n            </motion.div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      {/* Empty State */}\r\n      {filteredCases.length === 0 && (\r\n        <div className=\"text-center py-12\">\r\n          <MagnifyingGlassIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n          <p className=\"text-gray-500 dark:text-gray-400 mb-2\">\r\n            No se encontraron casos que coincidan con los filtros\r\n          </p>\r\n          <button\r\n            onClick={() => {\r\n              setSearchTerm(\"\");\r\n              setFilters({\r\n                type: \"all\",\r\n                location: \"all\",\r\n                budgetRange: \"all\",\r\n                urgencyLevel: \"all\",\r\n                sortBy: \"newest\",\r\n              });\r\n            }}\r\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium\"\r\n          >\r\n            Limpiar filtros\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Bid Modal */}\r\n      {showBidModal && selectedCase && (\r\n        <BidModal\r\n          case={selectedCase}\r\n          onClose={() => {\r\n            setShowBidModal(false);\r\n            setSelectedCase(null);\r\n          }}\r\n          onSubmit={(bidData) => handleBidSubmit(selectedCase.id, bidData)}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAEA;AACA;AACA;AAnBA;;;;;;;;;;AAqBO,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,MAAM;QACN,UAAU;QACV,aAAa;QACb,cAAc;QACd,QAAQ;IACV;IACA,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE7B,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,kBAAkB;gBAClB,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;eAAI;SAAe;QAElC,gBAAgB;QAChB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CACxB,CAAC,QACC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE9D;QAEA,cAAc;QACd,IAAI,QAAQ,IAAI,KAAK,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,QAAQ,IAAI;QACnE;QAEA,kBAAkB;QAClB,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAC,QAC1B,MAAM,cAAc,CACjB,WAAW,GACX,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW;QAE5C;QAEA,sBAAsB;QACtB,IAAI,QAAQ,WAAW,KAAK,OAAO;YACjC,MAAM,CAAC,KAAK,IAAI,GAAG,QAAQ,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YACtD,WAAW,SAAS,MAAM,CACxB,CAAC,QAAU,MAAM,cAAc,IAAI,OAAO,MAAM,cAAc,IAAI;QAEtE;QAEA,iBAAiB;QACjB,IAAI,QAAQ,YAAY,KAAK,OAAO;YAClC,WAAW,SAAS,MAAM,CACxB,CAAC,QAAU,MAAM,YAAY,KAAK,QAAQ,YAAY;QAE1D;QAEA,UAAU;QACV,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,SAAS,IAAI,CACX,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;gBAEjE;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc;gBAC3D;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc;gBAC3D;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;gBACjD;QACJ;QAEA,iBAAiB;IACnB,GAAG;QAAC;QAAgB;QAAY;KAAQ;IAExC,MAAM,kBAAkB,CAAC,QAAgB;QACvC,kDAAkD;QAClD,QAAQ,GAAG,CAAC,2BAA2B,QAAQ;QAE/C,+CAA+C;QAC/C,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;QAChE,MAAM,SAAS;YACb,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACvB;YACA,GAAG,OAAO;YACV,aAAa,IAAI,OAAO,WAAW;YACnC,QAAQ;QACV;QACA,SAAS,IAAI,CAAC;QACd,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;QAEhD,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO,6OAAA,CAAA,0BAAuB;YAChC,KAAK;gBACH,OAAO,iNAAA,CAAA,YAAS;YAClB,KAAK;gBACH,OAAO,iNAAA,CAAA,YAAS;YAClB,KAAK;gBACH,OAAO,6NAAA,CAAA,kBAAe;YACxB;gBACE,OAAO,iNAAA,CAAA,YAAS;QACpB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAE,WAAU;;oCACV,cAAc,MAAM;oCAAC;;;;;;;;;;;;;kCAK1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6OAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,8OAAC;;4CACE,eAAe,MAAM,CAAC,CAAC,IAAM,EAAE,YAAY,KAAK,UAAU,MAAM;4CAAE;4CAAI;;;;;;;;;;;;;0CAI3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,8OAAC;;4CAAK;4CACA;4CACH,KAAK,KAAK,CACT,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE,KACxD;4CACF;;;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;8CAC/B,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;kCAIhB,8OAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;0CAEV,8OAAC,mNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAM1C,6BACC,8OAAC,gJAAA,CAAA,mBAAgB;gBACf,SAAS;gBACT,iBAAiB;gBACjB,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,OAAO;oBACzB,MAAM,cAAc,eAAe,MAAM,YAAY;oBACrD,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;oBAChE,MAAM,SAAS,SAAS,IAAI,CAC1B,CAAC,MAA4B,IAAI,MAAM,KAAK,MAAM,EAAE;oBAGtD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAEd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,MAAM,IAAI;;;;;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,mNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;0EAAM,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kDAIjC,8OAAC;wCACC,WAAW,CAAC,oEAAoE,EAAE,gBAChF,MAAM,YAAY,GACjB;;0DAEH,8OAAC;gDAAY,WAAU;;;;;;4CACtB,MAAM,YAAY,KAAK,WACpB,YACA,MAAM,YAAY,KAAK,SACvB,SACA,MAAM,YAAY,KAAK,WACvB,UACA;;;;;;;;;;;;;0CAKR,8OAAC;gCAAE,WAAU;0CACV,MAAM,WAAW;;;;;;0CAIpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,mOAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;0DAC9B,8OAAC;gDAAK,WAAU;0DACb,MAAM,WAAW;;;;;;;;;;;;kDAGtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,yNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;;oDACb,MAAM,SAAS;oDAAC;;;;;;;;;;;;;kDAGrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DACb,YACG,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,MAAM,QAAQ,GAAG;oDAC5C,WAAW;oDACX,QAAQ,2IAAA,CAAA,KAAE;gDACZ,KACA;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;;4CAAmC;4CACtC,MAAM,UAAU;;;;;;;;;;;;;0CAK9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;4CACP,gBAAgB;4CAChB,gBAAgB;wCAClB;wCACA,UAAU;wCACV,WAAW,CAAC,kEAAkE,EAC5E,SACI,qFACA,2MACJ;kDAED,SAAS,sBAAsB;;;;;;kDAElC,8OAAC;wCAAO,WAAU;kDAAmU;;;;;;;;;;;;;uBAzFlV,MAAM,EAAE;;;;;gBA+FnB;;;;;;YAID,cAAc,MAAM,KAAK,mBACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qOAAA,CAAA,sBAAmB;wBAAC,WAAU;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,8OAAC;wBACC,SAAS;4BACP,cAAc;4BACd,WAAW;gCACT,MAAM;gCACN,UAAU;gCACV,aAAa;gCACb,cAAc;gCACd,QAAQ;4BACV;wBACF;wBACA,WAAU;kCACX;;;;;;;;;;;;YAOJ,gBAAgB,8BACf,8OAAC,yIAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,SAAS;oBACP,gBAAgB;oBAChB,gBAAgB;gBAClB;gBACA,UAAU,CAAC,UAAY,gBAAgB,aAAa,EAAE,EAAE;;;;;;;;;;;;AAKlE", "debugId": null}}, {"offset": {"line": 3935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\nimport {\r\n  BriefcaseIcon,\r\n  MagnifyingGlassIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { CaseList } from \"../_components/cases/CaseList\";\r\nimport { AvailableCases } from \"../_components/cases/AvailableCases\";\r\n\r\nexport default function DashboardPage() {\r\n  const [activeTab, setActiveTab] = useState(\"my-cases\");\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>\r\n        <Tabs.List className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg overflow-x-auto\">\r\n          <Tabs.Trigger\r\n            value=\"my-cases\"\r\n            className=\"flex items-center cursor-pointer px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700\"\r\n          >\r\n            <BriefcaseIcon className=\"h-4 w-4 mr-2\" />\r\n            Mis Casos\r\n          </Tabs.Trigger>\r\n          <Tabs.Trigger\r\n            value=\"available-cases\"\r\n            className=\"flex items-center cursor-pointer px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700\"\r\n          >\r\n            <MagnifyingGlassIcon className=\"h-4 w-4 mr-2\" />\r\n            Casos Disponibles\r\n          </Tabs.Trigger>\r\n        </Tabs.List>\r\n\r\n        <div className=\"mt-6\">\r\n          <Tabs.Content value=\"my-cases\">\r\n            <CaseList />\r\n          </Tabs.Content>\r\n          <Tabs.Content value=\"available-cases\">\r\n            <AvailableCases />\r\n          </Tabs.Content>\r\n        </div>\r\n      </Tabs.Root>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAIA;AACA;AATA;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gKAAA,CAAA,OAAS;YAAC,OAAO;YAAW,eAAe;;8BAC1C,8OAAC,gKAAA,CAAA,OAAS;oBAAC,WAAU;;sCACnB,8OAAC,gKAAA,CAAA,UAAY;4BACX,OAAM;4BACN,WAAU;;8CAEV,8OAAC,yNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG5C,8OAAC,gKAAA,CAAA,UAAY;4BACX,OAAM;4BACN,WAAU;;8CAEV,8OAAC,qOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gKAAA,CAAA,UAAY;4BAAC,OAAM;sCAClB,cAAA,8OAAC,wIAAA,CAAA,WAAQ;;;;;;;;;;sCAEX,8OAAC,gKAAA,CAAA,UAAY;4BAAC,OAAM;sCAClB,cAAA,8OAAC,8IAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}]}
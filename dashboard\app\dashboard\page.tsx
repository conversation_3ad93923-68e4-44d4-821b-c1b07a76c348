"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import * as Tabs from "@radix-ui/react-tabs";
import {
  BriefcaseIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { CaseList } from "../_components/cases/CaseList";
import { AvailableCases } from "../_components/cases/AvailableCases";

export default function DashboardPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("my-cases");

  // Sincronizar tab con URL params
  useEffect(() => {
    const tabFromUrl = searchParams.get("tab");
    if (tabFromUrl && (tabFromUrl === "my-cases" || tabFromUrl === "available-cases")) {
      setActiveTab(tabFromUrl);
    } else {
      // Si no hay tab en URL o es inválida, asegurar que esté en my-cases
      setActiveTab("my-cases");
    }
  }, [searchParams]);

  // Manejar cambio de tab y actualizar URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Actualizar URL sin recargar la página
    const newUrl = newTab === "my-cases" ? "/dashboard" : `/dashboard?tab=${newTab}`;
    router.replace(newUrl);
  };

  return (
    <div className="space-y-6">
      <Tabs.Root value={activeTab} onValueChange={handleTabChange}>
        <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg overflow-x-auto">
          <Tabs.Trigger
            value="my-cases"
            className="flex items-center cursor-pointer px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700"
          >
            <BriefcaseIcon className="h-4 w-4 mr-2" />
            Mis Casos
          </Tabs.Trigger>
          <Tabs.Trigger
            value="available-cases"
            className="flex items-center cursor-pointer px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700"
          >
            <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
            Casos Disponibles
          </Tabs.Trigger>
        </Tabs.List>

        <div className="mt-6">
          <Tabs.Content value="my-cases">
            <CaseList />
          </Tabs.Content>
          <Tabs.Content value="available-cases">
            <AvailableCases />
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </div>
  );
}

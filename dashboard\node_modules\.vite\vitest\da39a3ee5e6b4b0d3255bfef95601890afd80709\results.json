{"version": "3.2.3", "results": [[":app/_components/cases/__tests__/CaseList.test.tsx", {"duration": 3120.2622, "failed": true}], [":app/_components/__tests__/ThemeContext.test.tsx", {"duration": 295.5159000000001, "failed": false}], [":app/_components/__tests__/NotificationDropdown.test.tsx", {"duration": 1087.0870000000004, "failed": false}], [":app/_components/__tests__/AvatarDropdown.test.tsx", {"duration": 878.0624000000003, "failed": false}], [":app/_components/__tests__/TemplateCard.test.tsx", {"duration": 324.90560000000005, "failed": false}], [":app/_components/__tests__/TopBar.test.tsx", {"duration": 405.7925, "failed": false}], [":app/_components/__tests__/CaseDetailRoute.test.tsx", {"duration": 13.016000000000076, "failed": false}], [":app/_components/__tests__/Logo.test.tsx", {"duration": 107.37480000000005, "failed": false}], [":app/_components/__tests__/PurchaseModal.test.tsx", {"duration": 416.0715, "failed": false}], [":app/_components/__tests__/CaseFiltersPanel.test.tsx", {"duration": 320.6196, "failed": false}], [":app/_components/__tests__/UploadTemplateModal.test.tsx", {"duration": 310.8382999999999, "failed": false}], [":app/_components/__tests__/CaseIntelligence.test.tsx", {"duration": 121.55909999999994, "failed": false}], [":app/_components/__tests__/CaseContextPanel.test.tsx", {"duration": 125.11979999999994, "failed": false}], [":app/_components/__tests__/BidModal.test.tsx", {"duration": 11260.1906, "failed": true}]]}
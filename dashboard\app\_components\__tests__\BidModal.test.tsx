import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { BidModal } from "../modals/BidModal";
import { ThemeProvider } from "../../_contexts/ThemeContext";
import { AvailableCase } from "../../_lib/types";

// Mock framer-motion
vi.mock("framer-motion", () => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<"div">) => (
      <div {...props}>{children}</div>
    ),
  },
}));

// Mock matchMedia for theme detection
const mockMatchMedia = vi.fn();
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: mockMatchMedia,
});

const mockCase: AvailableCase = {
  id: "ac-001",
  title: "Despido sin causa - Empresa XYZ",
  type: "Laboral",
  clientName: "<PERSON>",
  clientLocation: "Buenos Aires, Argentina",
  description: "Caso de despido sin causa justa",
  budgetRange: "ARS 50,000 - 100,000",
  urgency: "medium",
  postedDate: "2025-01-15T10:00:00Z",
  deadline: "2025-02-15T10:00:00Z",
  requirements: ["Experiencia en derecho laboral", "Disponibilidad inmediata"],
  bidsCount: 3,
  status: "open",
};

interface TestAppProps {
  case?: AvailableCase;
  onClose?: () => void;
  onSubmit?: (bidData: any) => void;
  initialTheme?: "light" | "dark";
}

function TestApp({
  case: caseData = mockCase,
  onClose = vi.fn(),
  onSubmit = vi.fn(),
  initialTheme = "light",
}: TestAppProps) {
  // Set initial theme in localStorage before rendering
  if (typeof window !== "undefined") {
    localStorage.setItem("theme", initialTheme);
  }

  return (
    <ThemeProvider>
      <BidModal case={caseData} onClose={onClose} onSubmit={onSubmit} />
    </ThemeProvider>
  );
}

describe("BidModal", () => {
  beforeEach(() => {
    localStorage.clear();
    document.documentElement.className = "";
    vi.clearAllMocks();

    // Mock default matchMedia behavior
    mockMatchMedia.mockImplementation((query: string) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));
  });

  describe("Form Validation", () => {
    it("should show validation errors when submitting empty form with fixed fee", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Submit form without filling required fields
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      // Check that validation errors are displayed
      await waitFor(() => {
        expect(screen.getByText("El monto es obligatorio")).toBeInTheDocument();
        expect(screen.getByText("La descripción de honorarios es obligatoria")).toBeInTheDocument();
        expect(screen.getByText("El tiempo estimado es obligatorio")).toBeInTheDocument();
        expect(screen.getByText("La experiencia relevante es obligatoria")).toBeInTheDocument();
      });
    });

    it("should show validation errors when submitting empty form with contingency fee", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Select contingency fee type
      const contingencyRadio = screen.getByLabelText(/Cuota Litis/);
      await user.click(contingencyRadio);

      // Submit form without filling required fields
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      // Check that validation errors are displayed
      await waitFor(() => {
        expect(screen.getByText("El porcentaje es obligatorio")).toBeInTheDocument();
        expect(screen.getByText("La descripción de honorarios es obligatoria")).toBeInTheDocument();
        expect(screen.getByText("El tiempo estimado es obligatorio")).toBeInTheDocument();
        expect(screen.getByText("La experiencia relevante es obligatoria")).toBeInTheDocument();
      });
    });

    it("should validate fee amount is a positive number", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Fill invalid fee amount
      const feeAmountInput = screen.getByPlaceholderText("150000");
      await user.type(feeAmountInput, "-100");

      // Submit form
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      // Check validation error
      await waitFor(() => {
        expect(screen.getByText("Ingrese un monto válido mayor a 0")).toBeInTheDocument();
      });
    });

    it("should validate percentage is between 1 and 50", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Select contingency fee type
      const contingencyRadio = screen.getByLabelText(/Cuota Litis/);
      await user.click(contingencyRadio);

      // Fill invalid percentage
      const percentageInput = screen.getByPlaceholderText("30");
      await user.type(percentageInput, "60");

      // Submit form
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      // Check validation error
      await waitFor(() => {
        expect(screen.getByText("Ingrese un porcentaje válido entre 1 y 50")).toBeInTheDocument();
      });
    });

    it("should proceed to review step when all fields are valid", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Fill all required fields
      const feeAmountInput = screen.getByPlaceholderText("150000");
      await user.type(feeAmountInput, "50000");

      const feeDescriptionTextarea = screen.getByPlaceholderText(/Incluye todas las gestiones/);
      await user.type(feeDescriptionTextarea, "Honorarios por gestión completa del caso");

      const timelineInput = screen.getByPlaceholderText("3-6 meses");
      await user.type(timelineInput, "4 meses");

      const experienceTextarea = screen.getByPlaceholderText(/Describa su experiencia/);
      await user.type(experienceTextarea, "10 años de experiencia en derecho laboral");

      // Submit form
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      // Check that we moved to review step
      await waitFor(() => {
        expect(screen.getByText("Revisar Propuesta")).toBeInTheDocument();
        expect(screen.getByText("ARS 50.000 total")).toBeInTheDocument();
      });
    });

    it("should clear errors when modal is closed", async () => {
      const onClose = vi.fn();
      const user = userEvent.setup();
      render(<TestApp onClose={onClose} />);

      // Submit form to trigger validation errors
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      // Verify errors are shown
      await waitFor(() => {
        expect(screen.getByText("El monto es obligatorio")).toBeInTheDocument();
      });

      // Close modal
      const closeButton = screen.getByRole("button", { name: "" });
      await user.click(closeButton);

      expect(onClose).toHaveBeenCalledOnce();
    });
  });

  describe("Error Styling", () => {
    it("should apply red border to invalid fields", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Submit form to trigger validation
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      await waitFor(() => {
        const feeAmountInput = screen.getByPlaceholderText("150000");
        expect(feeAmountInput).toHaveClass("border-red-500");

        const feeDescriptionTextarea = screen.getByPlaceholderText(/Incluye todas las gestiones/);
        expect(feeDescriptionTextarea).toHaveClass("border-red-500");
      });
    });

    it("should show error messages in red text", async () => {
      const user = userEvent.setup();
      render(<TestApp />);

      // Submit form to trigger validation
      const submitButton = screen.getByText("Revisar Propuesta");
      await user.click(submitButton);

      await waitFor(() => {
        const errorMessage = screen.getByText("El monto es obligatorio");
        expect(errorMessage).toHaveClass("text-red-600", "dark:text-red-400");
      });
    });
  });
});
